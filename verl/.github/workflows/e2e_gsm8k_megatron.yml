name: e2e_gsm8k_megatron
# latest version: Megatron-LM core_r0.11.0 https://github.com/NVIDIA/Megatron-LM/tree/core_r0.11.0

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/e2e_gsm8k_megatron.yml
  pull_request:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - "verl/trainer/config/*.yaml"
      - .github/workflows/e2e_gsm8k_megatron.yml
      - "tests/e2e/*.sh"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions: 
  contents: read

jobs:
  e2e_gsm8k_megatron:
    runs-on: [self-hosted, l20-0]
    timeout-minutes: 40 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1"
      HF_HUB_ENABLE_HF_TRANSFER: 1
    container:
      image: whatcanyousee/verl:vemlp-th2.4.0-cu124-vllm0.6.3-ray2.10-te2.0-megatron0.11.0-v0.0.5
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
            fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install hf_transfer
          pip3 install -e .[test]
      - name: Prepare gsm8k dataset
        run: |
          python3 examples/data_preprocess/gsm8k.py
      - name: Running gsm8k e2e training tests with 3D parallelism on 8 L20 GPUs with Megatron (Deepseek)
        run: |
          ray stop --force
          export PYTHONPATH=$PYTHONPATH:/opt/nvidia/Megatron-LM
          bash tests/e2e/run_deepseek_megatron_parallelism.sh
      - name: Running gsm8k e2e training tests with 3D parallelism on 8 L20 GPUs with Megatron (Qwen)
        run: |
          ray stop --force
          export PYTHONPATH=$PYTHONPATH:/opt/nvidia/Megatron-LM
          bash tests/e2e/run_qwen_megatron_parallelism.sh