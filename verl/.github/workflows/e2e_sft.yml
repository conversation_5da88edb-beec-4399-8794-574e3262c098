name: e2e_sft

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/e2e_sft.yml
  pull_request:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/e2e_sft.yml
      - "tests/e2e/*.sh"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions: 
  contents: read

jobs:
  e2e_sft:
    runs-on: [self-hosted, l20-1]
    timeout-minutes: 5 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1"
      HF_HUB_ENABLE_HF_TRANSFER: 1
    container:
      image: verlai/verl:vemlp-th2.4.0-cu124-vllm0.6.3-ray2.10-te1.7-v0.0.3
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
            fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install hf_transfer
          pip3 install -e .[test,gpu]
      - name: Prepare gsm8k dataset
        run: |
          ray stop --force
          python3 examples/data_preprocess/gsm8k.py
      - name: Running gsm8k e2e training tests on 8 L20 GPUs with rmpad using function rm
        run: |
          ray stop --force
          bash tests/sft/run_sft.sh
      - name: Running gsm8k e2e training tests on 8 L20 GPUs with sequence parallism
        run: |
          ray stop --force
          bash examples/sft/gsm8k/run_qwen_05_sp2.sh 8 $HOME/ckpts/
      - name: Check loss difference between sequence parallel vs. default implementation
        run: |
          ray stop --force
          bash tests/sft/run_sft_sp_loss_match.sh
      - name: Running gsm8k e2e training tests on 8 L20 GPUs with sequence parallism and liger
        run: |
          ray stop --force
          bash tests/sft/run_sft_qwen05_sp2_liger.sh 8 $HOME/ckpts/
          rm -rf $HOME/ckpts/
