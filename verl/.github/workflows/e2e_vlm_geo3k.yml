name: e2e_vlm_geo3k

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/e2e_vlm_geo3k.yml
  pull_request:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/e2e_vlm_geo3k.yml
      - "tests/e2e/*.sh"

# Declare permissions just read content.
permissions: 
  contents: read

jobs:
  e2e_vlm_geo3k:
    runs-on: [self-hosted, l20-1]
    timeout-minutes: 10 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1"
      HF_HUB_ENABLE_HF_TRANSFER: 1
    container:
      image: hiyouga/verl:ngc-th2.5.1-cu120-vllm0.7.3-rc1
      options: --gpus all --shm-size=40g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
            fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install hf_transfer
          pip3 install --no-deps -e .[test,gpu,geo]
          pip3 install --no-deps mathruler
          python -c "import transformers; print(transformers.__version__)"
      - name: Prepare geo3k dataset
        run: |
          ray stop --force
          python3 examples/data_preprocess/geo3k.py
      - name: Running geo3k vlm e2e training tests on 8 L20 GPUs with rmpad using function rm
        run: |
          ray stop --force
          bash tests/e2e/run_qwen2vl_geo3k_function_rm.sh
