name: vllm

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/vllm.yml
  pull_request:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - "verl/trainer/config/*.yaml"
      - .github/workflows/vllm.yml

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions: 
  contents: read

jobs:
  vllm:
    runs-on: [self-hosted, l20-0]
    timeout-minutes: 20 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1"
      HF_HUB_ENABLE_HF_TRANSFER: 1
    container:
      image: verlai/verl:vemlp-th2.4.0-cu124-vllm0.6.3-ray2.10-te1.7-v0.0.3
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
            fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install hf_transfer
          pip3 install -e .[test]
          pip3 install vllm==0.5.4
      - name: Running vllm tests on 8 L20 GPUs
        run: |
          cd tests/rollout
          torchrun --standalone --nnodes=1 --nproc_per_node=8 $(which pytest) -s test_vllm_hf_loader.py
      - name: Test the latest vLLM
        run: |
          pip3 install --upgrade vllm
          cd tests/rollout
          torchrun --standalone --nnodes=1 --nproc_per_node=4 $(which pytest) -s test_vllm_spmd.py
      - name: Run Qwen 0.5B generation test
        run: |
          cd tests/generation
          bash ./run_gen_qwen05.sh 4 $HOME/data/gen/qwen_05_gen_test.parquet 2
          rm -rf $HOME/data/gen/qwen_05_gen_test.parquet
      - name: Run Qwen 0.5B generation test when world_size == 1
        run: |
          cd tests/generation
          bash ./run_gen_qwen05.sh 1 $HOME/data/gen/qwen_05_gen_test.parquet 1
          rm -rf $HOME/data/gen/qwen_05_gen_test.parquet
