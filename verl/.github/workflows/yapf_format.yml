name: yapf

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/yapf_format.yml
  pull_request:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/yapf_format.yml

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions: 
  contents: read

jobs:
  yapf:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      # - name: checkout
      #   run: |
      #     commits=${{ github.event.pull_request.commits }}
      #     if [[ -n "$commits" ]]; then
      #       # Prepare enough depth for diffs with main
      #       git fetch --depth="$(( commits + 1 ))"
      #     fi
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b # v5.3.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install --upgrade yapf
          pip install toml==0.10.2
      - name: Running yapf
        run: |
          yapf -r -vv -d --style=./.style.yapf verl tests examples
